import { ApiResponse, OrderArray, OrderDirection, PaginatedResponse } from '@/types';
import { TagDescription, SerializeQueryArgs } from '@reduxjs/toolkit/query';

export type FlexibleApiInstance = {
  util: { updateQueryData: any; prefetch: any; invalidateTags: any };
  endpoints: { [key: string]: { select: (args: any) => (state: any) => any } };
};

// Enhanced invalidation strategy types
export type InvalidationStrategy =
  | 'individual' // Invalidate specific item by ID
  | 'group' // Invalidate by group identifier (e.g., utteranceId)
  | 'conditional' // Invalidate based on custom conditions
  | 'cascade' // Invalidate related entities
  | 'selective'; // Invalidate specific cache entries

export interface InvalidationRule<Entity = any> {
  strategy: InvalidationStrategy;
  getTargets: (entity: Entity, context?: any) => string[];
  condition?: (entity: Entity, context?: any) => boolean;
  cascadeRules?: InvalidationRule<Entity>[];
}

export interface GroupInvalidationConfig {
  groupKey: string;
  getGroupId: (entity: any) => string;
  relatedEntityTypes?: string[];
}

export interface OptimizedTagConfig<T extends string> {
  entityType: T;
  invalidationRules?: InvalidationRule[];
  groupConfigs?: GroupInvalidationConfig[];
  debugMode?: boolean;
}

export interface GetIdOptions<T> {
  getId?: (arg: T) => string;
}

export interface CacheRelationship {
  entityType: string;
  relationKey: string;
  getRelatedIds: (entity: any) => string[];
}

interface EntityConfig<
  Entity,
  TagType extends string,
  ApiInstance extends FlexibleApiInstance,
  QueryArg,
> {
  entityType: TagType;
  apiInstance: ApiInstance;
  getSerializeQueryArgs: SerializeQueryArgs<any>;
  paginationMerge?: <T>(
    currentCache: ApiResponse<PaginatedResponse<T>> | undefined,
    newItems: ApiResponse<PaginatedResponse<T>>,
    meta: { arg: any }
  ) => ApiResponse<PaginatedResponse<T>>;
  options?: {
    listEndpointName?: string;
    itemEndpointName?: string;
    itemPrefetch?: boolean;
  };
  shouldUpdateCache?: (queryArgs: QueryArg, initialArgs: any, newEntity: Entity) => boolean;
  getItemQueryArgs?: (entity: Entity) => QueryArg;
  invalidationRules?: InvalidationRule<Entity>[];
  relationships?: CacheRelationship[];
  groupConfigs?: GroupInvalidationConfig[];
}

// Enhanced invalidation context for debugging and monitoring
export interface InvalidationContext {
  operation: 'create' | 'update' | 'delete';
  entityId: string;
  entityType: string;
  timestamp: number;
  metadata?: Record<string, unknown>;
}

// Cache performance metrics
export interface CacheMetrics {
  invalidationCount: number;
  lastInvalidation: number;
  hitRate: number;
  missRate: number;
}

export const createOptimizedTags = <T extends string>(config: OptimizedTagConfig<T>) => {
  const { entityType, invalidationRules = [], groupConfigs = [], debugMode = false } = config;

  // Debug logging utility
  const debugLog = (message: string, data?: unknown) => {
    if (debugMode) {
      console.log(`[OptimizedTags:${entityType}] ${message}`, data);
    }
  };

  const createListTag = (): TagDescription<T> => ({ type: entityType, id: 'LIST' });
  const createEntityTag = (id: string): TagDescription<T> => ({ type: entityType, id });
  const createGroupTag = (groupKey: string, groupId: string): TagDescription<T> => ({
    type: entityType,
    id: `${groupKey}:${groupId}`,
  });

  // Enhanced invalidation with multiple strategies
  const executeInvalidationRules = (
    entity: unknown,
    context: InvalidationContext
  ): TagDescription<T>[] => {
    const tags: TagDescription<T>[] = [];

    debugLog('Executing invalidation rules', {
      entity,
      context,
      rulesCount: invalidationRules.length,
    });

    for (const rule of invalidationRules) {
      if (rule.condition && !rule.condition(entity, context)) {
        continue;
      }

      const targets = rule.getTargets(entity, context);
      debugLog(`Rule ${rule.strategy} generated targets`, targets);

      switch (rule.strategy) {
        case 'individual':
          targets.forEach(id => tags.push(createEntityTag(id)));
          break;
        case 'group':
          targets.forEach(groupId => {
            const groupConfig = groupConfigs.find(gc => gc.groupKey === 'default');
            if (groupConfig) {
              tags.push(createGroupTag(groupConfig.groupKey, groupId));
            }
          });
          break;
        case 'conditional':
        case 'cascade':
        case 'selective':
          targets.forEach(id => tags.push(createEntityTag(id)));
          if (rule.cascadeRules) {
            rule.cascadeRules.forEach(cascadeRule => {
              const cascadeTags = executeInvalidationRules(entity, context);
              tags.push(...cascadeTags);
            });
          }
          break;
      }
    }

    return tags;
  };

  return {
    providesList: (): TagDescription<T>[] => [createListTag()],

    providesItem: (_result: unknown, _error: unknown, arg: { id: string }): TagDescription<T>[] => {
      const tags = [createEntityTag(arg.id)];

      // Add group tags if configured
      groupConfigs.forEach(groupConfig => {
        if (_result && typeof _result === 'object' && 'data' in _result) {
          const entity = (_result as { data: unknown }).data;
          if (entity) {
            const groupId = groupConfig.getGroupId(entity);
            if (groupId) {
              tags.push(createGroupTag(groupConfig.groupKey, groupId));
            }
          }
        }
      });

      return tags;
    },

    invalidatesItem: (id: string): TagDescription<T>[] => [createEntityTag(id)],

    invalidatesList: (): TagDescription<T>[] => [createListTag()],

    // New enhanced invalidation methods
    invalidateByRules: (
      entity: unknown,
      operation: 'create' | 'update' | 'delete'
    ): TagDescription<T>[] => {
      const context: InvalidationContext = {
        operation,
        entityId:
          typeof entity === 'object' && entity && 'id' in entity ? String(entity.id) : 'unknown',
        entityType,
        timestamp: Date.now(),
      };

      return executeInvalidationRules(entity, context);
    },

    invalidateByGroup: (groupKey: string, groupId: string): TagDescription<T>[] => {
      debugLog('Invalidating by group', { groupKey, groupId });
      return [createGroupTag(groupKey, groupId)];
    },

    invalidateMultiple: (ids: string[]): TagDescription<T>[] => {
      return ids.map(id => createEntityTag(id));
    },

    // Utility methods
    createEntityTag,
    createGroupTag,
    createListTag,
  };
};

const multiCompare = <T extends Record<string, any>>(a: T, b: T, orders: OrderArray): number => {
  for (const [field, dir] of orders) {
    const va = a[field];
    const vb = b[field];

    const cmp = compareValues(va, vb);
    if (cmp !== 0) {
      return dir === OrderDirection.ASC ? cmp : -cmp;
    }
  }

  return 0;
};

const compareValues = (va: any, vb: any): number => {
  if (va == null && vb == null) return 0;
  if (va == null) return 1;
  if (vb == null) return -1;

  if (typeof va === 'string' && typeof vb === 'string') {
    return va.localeCompare(vb);
  }

  return comparePrimitive(va, vb);
};

const comparePrimitive = (va: any, vb: any): number => {
  if (va < vb) return -1;
  if (va > vb) return 1;
  return 0;
};

const getOrderBy = <QueryArg extends Record<string, any>>(cacheKey: QueryArg) => {
  const order = cacheKey?.order ?? cacheKey?.query?.order;
  return order;
};

// Enhanced cache update strategies
export interface CacheUpdateStrategy<T = unknown> {
  name: string;
  shouldUpdate: (item: T, context: InvalidationContext) => boolean;
  updateItem: (draft: ApiResponse<PaginatedResponse<T>>, item: T, orderBy?: OrderArray) => void;
}

// Selective cache update configuration
export interface SelectiveCacheConfig<T = unknown> {
  updateStrategies: CacheUpdateStrategy<T>[];
  batchSize?: number;
  debounceMs?: number;
}

export const createAdvancedCacheUpdaters = <T extends { id: string }>() => {
  // Performance tracking
  const metrics: CacheMetrics = {
    invalidationCount: 0,
    lastInvalidation: 0,
    hitRate: 0,
    missRate: 0,
  };

  // Batch update queue for performance optimization
  const updateQueue: Array<{
    draft: ApiResponse<PaginatedResponse<T>>;
    item: T;
    operation: 'add' | 'update' | 'remove';
    orderBy?: OrderArray;
  }> = [];

  const flushUpdateQueue = () => {
    const operations = updateQueue.splice(0);
    operations.forEach(({ draft, item, operation, orderBy }) => {
      switch (operation) {
        case 'add':
          addItemToListsInternal(draft, item, orderBy);
          break;
        case 'update':
          updateItemInListsInternal(draft, item, orderBy);
          break;
        case 'remove':
          removeItemFromListsInternal(draft, item.id);
          break;
      }
    });
  };

  const updateItemInListsInternal = (
    draft: ApiResponse<PaginatedResponse<T>>,
    updatedItem: T,
    orderBy?: OrderArray
  ) => {
    if (!draft?.data?.items) return;

    const index = draft.data.items.findIndex(item => item.id === updatedItem.id);
    if (index === -1) return;

    // Remove the old item
    draft.data.items.splice(index, 1);

    let insertIndex = index;
    if (orderBy?.length) {
      insertIndex = draft.data.items.findIndex(
        item => multiCompare(item, updatedItem, orderBy) > 0
      );
    }

    draft.data.items.splice(
      insertIndex === -1 ? draft.data.items.length : insertIndex,
      0,
      updatedItem
    );
  };

  const removeItemFromListsInternal = (
    draft: ApiResponse<PaginatedResponse<T>>,
    itemId: string
  ) => {
    if (!draft?.data?.items) return;

    const initialLength = draft.data.items.length;
    draft.data.items = draft.data.items.filter(item => item.id !== itemId);

    if (draft.data.pagination && draft.data.items.length < initialLength) {
      draft.data.pagination.total = Math.max(0, draft.data.pagination.total - 1);
    }
  };

  const addItemToListsInternal = (
    draft: ApiResponse<PaginatedResponse<T>>,
    newItem: T,
    orderBy?: OrderArray
  ) => {
    if (!draft?.data?.items) return;

    // Check if item already exists to prevent duplicates
    const existingIndex = draft.data.items.findIndex(item => item.id === newItem.id);
    if (existingIndex !== -1) {
      draft.data.items[existingIndex] = newItem;
      return;
    }

    let insertIndex = 0;
    if (orderBy?.length) {
      insertIndex = draft.data.items.findIndex(item => multiCompare(item, newItem, orderBy) > 0);
    }

    draft.data.items.splice(insertIndex === -1 ? draft.data.items.length : insertIndex, 0, newItem);

    if (draft.data.pagination) {
      draft.data.pagination.total += 1;
    }
  };

  return {
    // Original methods for backward compatibility
    updateItemInLists: updateItemInListsInternal,
    removeItemFromLists: removeItemFromListsInternal,
    addItemToLists: addItemToListsInternal,

    // Enhanced methods with performance optimization
    batchUpdateItems: (
      updates: Array<{
        draft: ApiResponse<PaginatedResponse<T>>;
        item: T;
        operation: 'add' | 'update' | 'remove';
        orderBy?: OrderArray;
      }>
    ) => {
      updateQueue.push(...updates);
      // Use requestIdleCallback if available, otherwise setTimeout
      if (typeof requestIdleCallback !== 'undefined') {
        requestIdleCallback(flushUpdateQueue);
      } else {
        setTimeout(flushUpdateQueue, 0);
      }
    },

    // Selective update based on conditions
    conditionalUpdate: (
      draft: ApiResponse<PaginatedResponse<T>>,
      item: T,
      condition: (existingItem: T) => boolean,
      orderBy?: OrderArray
    ) => {
      if (!draft?.data?.items) return;

      const existingItem = draft.data.items.find(existing => existing.id === item.id);
      if (existingItem && condition(existingItem)) {
        updateItemInListsInternal(draft, item, orderBy);
      }
    },

    // Group-based updates for related items
    updateRelatedItems: (
      draft: ApiResponse<PaginatedResponse<T>>,
      groupId: string,
      getGroupId: (item: T) => string,
      updateFn: (item: T) => T,
      orderBy?: OrderArray
    ) => {
      if (!draft?.data?.items) return;

      const updatedItems: T[] = [];
      draft.data.items.forEach((item, index) => {
        if (getGroupId(item) === groupId) {
          const updatedItem = updateFn(item);
          draft.data!.items[index] = updatedItem;
          updatedItems.push(updatedItem);
        }
      });

      // Re-sort if necessary
      if (orderBy?.length && updatedItems.length > 0) {
        draft.data.items.sort((a, b) => multiCompare(a, b, orderBy));
      }
    },

    // Performance metrics
    getMetrics: () => ({ ...metrics }),
    resetMetrics: () => {
      metrics.invalidationCount = 0;
      metrics.lastInvalidation = 0;
      metrics.hitRate = 0;
      metrics.missRate = 0;
    },
  };
};

export const defaultSerializeListArgs: SerializeQueryArgs<any> = ({ queryArgs, endpointName }) => {
  return `${endpointName}-${JSON.stringify(queryArgs)}`;
};

export const createInfiniteScrollHelpers = () => ({
  mergePaginatedData: <T>(
    existing: ApiResponse<PaginatedResponse<T>> | undefined,
    incoming: ApiResponse<PaginatedResponse<T>>
  ): ApiResponse<PaginatedResponse<T>> => {
    if (!existing?.data) return incoming;

    // Prevent duplicate items by filtering out existing IDs.
    // This relies on `item.id` being a unique identifier for each item. If duplicates persist, ensure `item.id` is truly unique and consistent.
    const existingIds = new Set((existing.data.items ?? []).map((item: any) => item.id));
    const newItems = (incoming.data?.items ?? []).filter((item: any) => !existingIds.has(item.id));

    return {
      ...incoming,
      data: {
        ...incoming.data,
        items: [...(existing.data.items ?? []), ...newItems],
        pagination: incoming.data!.pagination,
      },
    };
  },
  hasMorePages: (data: ApiResponse<PaginatedResponse<unknown>> | undefined): boolean => {
    return data?.data?.pagination?.hasNext ?? false;
  },
  getNextPage: (data: ApiResponse<PaginatedResponse<unknown>> | undefined): number => {
    const currentPage = data?.data?.pagination?.page ?? 1;
    return currentPage + 1;
  },
});

export const defaultPaginatedMerge = <T, QueryArg>(
  currentCache: ApiResponse<PaginatedResponse<T>> | undefined,
  newItems: ApiResponse<PaginatedResponse<T>>,
  _meta: { arg: QueryArg }
): ApiResponse<PaginatedResponse<T>> => {
  return createInfiniteScrollHelpers().mergePaginatedData(currentCache, newItems);
};

export const defaultForceRefetch = <QueryArg>({
  currentArg,
  previousArg,
}: {
  currentArg?: QueryArg;
  previousArg?: QueryArg;
}) => {
  const current = currentArg as any;
  const previous = previousArg as any;

  return JSON.stringify(current ?? {}) !== JSON.stringify(previous ?? {});
};

// Enhanced entity mixins with flexible configuration
export const createEntityMixins = <
  Entity extends { id: string },
  TagType extends string,
  ApiInstance extends FlexibleApiInstance,
  QueryArg extends Record<string, unknown>,
>(
  config: EntityConfig<Entity, TagType, ApiInstance, QueryArg>
) => {
  const {
    entityType,
    getSerializeQueryArgs,
    paginationMerge,
    invalidationRules = [],
    groupConfigs = [],
  } = config;

  const tags = createOptimizedTags<TagType>({
    entityType,
    invalidationRules,
    groupConfigs,
    debugMode: process.env.NODE_ENV === 'development',
  });

  const paginatedMixin = getPaginatedListQueryMixin<TagType, QueryArg>({
    tags,
    options: {
      serializeQueryArgs: getSerializeQueryArgs,
      merge: paginationMerge ?? defaultPaginatedMerge,
      forceRefetch: defaultForceRefetch,
    },
  });

  const itemMixin = getItemQueryMixin<TagType>({ tags });

  const { create, update, remove } = getEnhancedMutationMixins(config);

  return {
    paginated: paginatedMixin,
    item: itemMixin,
    create,
    update,
    delete: remove,
    tags,
    // Enhanced utilities
    invalidateByGroup: (groupKey: string, groupId: string) =>
      tags.invalidateByGroup(groupKey, groupId),
    invalidateMultiple: (ids: string[]) => tags.invalidateMultiple(ids),
    invalidateByRules: (entity: Entity, operation: 'create' | 'update' | 'delete') =>
      tags.invalidateByRules(entity, operation),
  };
};

export const getPaginatedListQueryMixin = <TagType extends string, QueryArg>(config: {
  tags: ReturnType<typeof createOptimizedTags<TagType>>;
  options?: {
    serializeQueryArgs?: SerializeQueryArgs<QueryArg>;
    merge?: <T>(
      currentCache: ApiResponse<PaginatedResponse<T>> | undefined,
      newItems: ApiResponse<PaginatedResponse<T>>,
      meta: { arg: QueryArg }
    ) => ApiResponse<PaginatedResponse<T>>;
    forceRefetch?: (args: { currentArg?: QueryArg; previousArg?: QueryArg }) => boolean;
  };
}) => {
  const { tags, options = {} } = config;
  const {
    serializeQueryArgs = defaultSerializeListArgs,
    merge = defaultPaginatedMerge,
    forceRefetch = defaultForceRefetch,
  } = options;

  return {
    providesTags: tags.providesList,
    serializeQueryArgs,
    merge,
    forceRefetch,
  };
};

export const getItemQueryMixin = <TagType extends string>(config: {
  tags: ReturnType<typeof createOptimizedTags<TagType>>;
}) => {
  const { tags } = config;
  return {
    providesTags: tags.providesItem,
  };
};

const defaultGetId = <T extends object>(arg: T) => (arg as any).id;

// Enhanced mutation mixins with flexible invalidation
export const getEnhancedMutationMixins = <
  Entity extends { id: string },
  TagType extends string,
  ApiInstance extends FlexibleApiInstance,
  QueryArg extends Record<string, unknown>,
>(
  config: EntityConfig<Entity, TagType, ApiInstance, QueryArg>
) => {
  const {
    entityType,
    shouldUpdateCache,
    apiInstance,
    getItemQueryArgs,
    options,
    invalidationRules = [],
    groupConfigs = [],
  } = config;

  const apiUtil = apiInstance.util;

  const tags = createOptimizedTags<TagType>({
    entityType,
    invalidationRules,
    groupConfigs,
  });
  const updaters = createAdvancedCacheUpdaters<Entity>();

  const { listEndpointName = `get${entityType}s`, itemEndpointName = `get${entityType}` } =
    options ?? {};

  const create = <CreateArg>() => ({
    async onQueryStarted(arg: CreateArg, api: any) {
      const { dispatch, queryFulfilled, getState } = api;
      try {
        const { data: newEntityData } = await queryFulfilled;
        if (newEntityData.success && newEntityData.data) {
          const state = getState();
          const apiState = state.api;
          if (!newEntityData.data) {
            throw new Error('Mutation response data is undefined or null.');
          }

          const updatedEntityData: Entity = newEntityData.data;

          if (options?.itemPrefetch) {
            const itemQueryArgs = getItemQueryArgs?.(updatedEntityData) ?? {
              id: updatedEntityData.id,
            };
            await dispatch(apiUtil.prefetch(itemEndpointName, itemQueryArgs, { force: true }));

            //TODO: need to check if this is needed or not
            // apiInstance.endpoints[itemEndpointName].select(
            //   config.getSerializeQueryArgs({
            //     queryArgs: itemQueryArgs,
            //     endpointName: itemEndpointName,
            //     endpointDefinition: apiInstance.endpoints[itemEndpointName],
            //   })
            // )(getState());
          }

          Object.keys(apiState.queries).forEach(key => {
            const query = apiState.queries[key];
            if (query?.endpointName === listEndpointName) {
              const cacheKeyArgs = query.originalArgs;

              const shouldProceed =
                shouldUpdateCache?.(cacheKeyArgs, arg, updatedEntityData) ?? true;

              if (shouldProceed) {
                dispatch(
                  apiUtil.updateQueryData(
                    listEndpointName,
                    cacheKeyArgs,
                    (draft: ApiResponse<PaginatedResponse<Entity>>) => {
                      const orderBy = getOrderBy(cacheKeyArgs);
                      updaters.addItemToLists(draft, updatedEntityData, orderBy);
                    }
                  )
                );
              }
            }
          });
        }
      } catch {
        console.log('Error in onQueryStarted. Undoing patchResult.');
      }
    },
  });

  const update = <UpdateArg extends object>(localOptions: GetIdOptions<UpdateArg> = {}) => {
    const { getId = defaultGetId<UpdateArg> } = localOptions;
    return {
      invalidatesTags: (_result: unknown, _error: unknown, arg: UpdateArg) =>
        tags.invalidatesItem(getId(arg)),

      async onQueryStarted(arg: UpdateArg, api: any) {
        const { dispatch, queryFulfilled, getState } = api;
        const id = getId(arg);

        const patchResult = dispatch(
          apiUtil.updateQueryData(itemEndpointName, { id }, (draft: ApiResponse<Entity>) => {
            if (draft.data) {
              Object.assign(draft.data, arg);
            }
          })
        );

        try {
          const { data: updatedEntityResponse } = await queryFulfilled;
          if (updatedEntityResponse.success && updatedEntityResponse.data) {
            const updatedEntityData: Entity = updatedEntityResponse.data;

            if (options?.itemPrefetch) {
              const itemQueryArgs = getItemQueryArgs?.(updatedEntityData) ?? {
                id: updatedEntityData.id,
              };
              await dispatch(apiUtil.prefetch(itemEndpointName, itemQueryArgs, { force: true }));

              //TODO: need to check if this is needed or not
              // apiInstance.endpoints[itemEndpointName].select(
              //   config.getSerializeQueryArgs({
              //     queryArgs: itemQueryArgs,
              //     endpointName: itemEndpointName,
              //     endpointDefinition: apiInstance.endpoints[itemEndpointName],
              //   })
              // )(getState())?.data;
            }

            if (updatedEntityData) {
              const state = getState();
              const apiState = state.api;

              Object.keys(apiState.queries).forEach(key => {
                const query = apiState.queries[key];
                if (query?.endpointName === listEndpointName) {
                  const cacheKeyArgs = query.originalArgs;

                  dispatch(
                    apiUtil.updateQueryData(
                      listEndpointName,
                      cacheKeyArgs,
                      (draft: ApiResponse<PaginatedResponse<Entity>>) => {
                        const orderBy = getOrderBy(cacheKeyArgs);
                        updaters.updateItemInLists(draft, updatedEntityData, orderBy);
                      }
                    )
                  );
                }
              });
            }
          }
        } catch (error) {
          console.log('Error in onQueryStarted (update). Undoing patchResult.', error);
          (patchResult as { undo: () => void }).undo();
        }
      },
    };
  };

  const remove = <RemoveArg extends object>(options: GetIdOptions<RemoveArg> = {}) => {
    const { getId = defaultGetId<RemoveArg> } = options;
    return {
      invalidatesTags: (_result: unknown, _error: unknown, arg: RemoveArg) =>
        tags.invalidatesItem(getId(arg)),

      async onQueryStarted(arg: RemoveArg, api: any) {
        const { dispatch, queryFulfilled, getState } = api;
        const id = getId(arg);
        const patchResults: Array<{ undo: () => void }> = [];

        try {
          const state = getState();
          const api = state.api;

          Object.keys(api.queries).forEach(key => {
            const query = api.queries[key];
            if (query?.endpointName === listEndpointName) {
              const cacheKey = query.originalArgs;

              const patchResult = dispatch(
                apiUtil.updateQueryData(
                  listEndpointName,
                  cacheKey,
                  (draft: ApiResponse<PaginatedResponse<Entity>>) => {
                    updaters.removeItemFromLists(draft, id);
                  }
                )
              );
              patchResults.push(patchResult as { undo: () => void });
            }
          });

          await queryFulfilled;
        } catch {
          patchResults.forEach(result => result.undo());
        }
      },
    };
  };

  return {
    create,
    update,
    remove,
  };
};
